/**
 * PopupWindow.css
 *
 * Styles for the popup window component
 */

/* 弹出窗口 */
.rs-popup {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 520px;
  max-height: 70vh;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  display: none;
  flex-direction: column;
  animation: rs-popup-show 0.3s ease;
  overflow: hidden;
}

@keyframes rs-popup-show {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes rs-popup-hide {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(20px);
    opacity: 0;
  }
}

@keyframes rs-paper-item-fadeout {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
    max-height: 100px;
    margin-bottom: 8px;
    padding: 12px;
  }
  50% {
    opacity: 0.5;
    transform: translateX(-10px) scale(0.95);
  }
  100% {
    opacity: 0;
    transform: translateX(-30px) scale(0.8);
    max-height: 0;
    margin-bottom: 0;
    padding: 0;
  }
}

@keyframes rs-notification-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes rs-notification-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 弹出窗口头部 */
.rs-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #4285f4;
  color: white;
}

.rs-popup-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.rs-popup-close {
  background: none !important;
  border: none;
  color: white !important;
  font-size: 24px;
  cursor: pointer;
  padding: 0 !important;
  margin: 0 !important;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  border-radius: 50%;
}

.rs-popup-close:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* 内容包装器 */
.rs-popup-content-wrapper {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

/* 弹出窗口内容 */
.rs-popup-content {
  padding: 16px;
  padding-bottom: 0; /* Remove bottom padding to avoid double padding with fixed action buttons */
  overflow-y: auto;
  flex: 1;
}

.rs-popup-query {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 15px;
}

.rs-popup-paper-count {
  color: #666;
  margin-bottom: 16px;
  font-size: 14px;
}

/* 批处理按钮 */
.rs-popup-batch-actions {
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

/* 论文列表容器 */
.rs-popup-paper-list-container {
  overflow-y: auto;
  max-height: calc(50vh - 120px); /* Adjust for header and other fixed elements */
  margin-bottom: 16px;
  border: none; /* 去掉边框 */
  border-radius: 6px;
  padding: 8px;
  box-shadow: none; /* 去掉阴影 */
}

/* 论文列表 */
.rs-popup-paper-list {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 减少一半：16px -> 8px */
}

.rs-popup-paper-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background-color: #f8f9fa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.rs-popup-paper-item.rs-fadeout {
  animation: rs-paper-item-fadeout 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.rs-popup.rs-hiding {
  animation: rs-popup-hide 0.3s cubic-bezier(0.4, 0, 1, 1) forwards;
}

.rs-popup-paper-info {
  flex: 1;
}

.rs-popup-paper-title {
  font-weight: 500;
  margin-bottom: 4px;
  font-size: 15px;
}

.rs-popup-paper-authors,
.rs-popup-paper-year {
  color: #666;
  font-size: 13px;
}

.rs-popup-paper-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Toggle switch styles */
.rs-toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.rs-toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.rs-toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.rs-toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .rs-toggle-slider {
  background-color: #2196F3;
}

input:checked + .rs-toggle-slider:before {
  transform: translateX(26px);
}

/* 操作按钮固定在底部 */
.rs-action-buttons-fixed {
  position: sticky;
  bottom: 0;
  background-color: white;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  z-index: 1;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05);
}

/* Paper remove button (SVG icon) */
.rs-popup-paper-remove {
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 0 8px;
  align-self: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rs-popup-paper-remove:hover {
  color: #f44336;
}

.rs-popup-paper-remove svg {
  width: 18px;
  height: 18px;
  stroke: currentColor;
}

/* Delete icon image */
.rs-delete-icon {
  width: 18px;
  height: 18px;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.rs-popup-paper-remove:hover .rs-delete-icon {
  opacity: 1;
}

/* Action buttons section */
.rs-action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Toggle options */
.rs-toggle-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

/* Start organize button */
.rs-start-organize-btn {
  margin-top: 10px;
  padding: 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  display: inline-flex;           /* 垂直居中关键 */
  align-items: center;            /* 垂直居中关键 */
  justify-content: center;        /* 水平居中 */
  line-height: 1;                 /* 避免基线偏移 */
  text-align: center;
  transition: background-color 0.3s ease;
}

.rs-start-organize-btn:hover {
  background-color: #45a049;
}

/* Loading and success states */
.rs-loading {
  position: relative;
  color: transparent !important;
}

.rs-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.rs-success {
  background-color: #34a853 !important;
}

/* 新增配置区域样式 */
.rs-config-section {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.rs-language-select,
.rs-standard-select {
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.rs-language-select:focus,
.rs-standard-select:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.rs-edit-prompt-btn {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.rs-edit-prompt-btn:hover {
  background-color: #e8e8e8;
}

.rs-edit-prompt-btn:active {
  background-color: #d8d8d8;
}

/* 存储路径设置样式 */
.rs-storage-section {
  margin-top: 12px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.rs-storage-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 13px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 6px;
}

.rs-storage-row {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.rs-storage-label {
  font-size: 12px;
  margin-bottom: 4px;
  color: #666;
  font-weight: 500;
}

.rs-working-dir-input,
.rs-task-dir-input {
  flex: 1;
  padding: 6px 8px;
  font-size: 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s ease;
}

.rs-working-dir-input:focus,
.rs-task-dir-input:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
}

.rs-working-dir-input[readonly] {
  background-color: #f8f9fa;
  color: #6c757d;
}

.rs-browse-btn,
.rs-auto-gen-btn {
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  white-space: nowrap;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.rs-browse-btn {
  background-color: #007cba;
  color: white;
}

.rs-browse-btn:hover {
  background-color: #005a87;
  transform: translateY(-1px);
}

.rs-auto-gen-btn {
  background-color: #28a745;
  color: white;
}

.rs-auto-gen-btn:hover {
  background-color: #1e7e34;
  transform: translateY(-1px);
}

.rs-browse-btn:active,
.rs-auto-gen-btn:active {
  transform: translateY(0);
}

.rs-path-preview {
  padding: 8px;
  font-size: 11px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  color: #495057;
  word-break: break-all;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.4;
  margin-top: 4px;
}

.rs-path-preview:empty::before {
  content: '请先选择工作目录';
  color: #adb5bd;
  font-style: italic;
}

/* 存储路径容器 */
.rs-storage-container {
  margin-bottom: 8px;
}

.rs-storage-container:last-child {
  margin-bottom: 0;
}

/* 路径预览容器 */
.rs-path-preview-container {
  margin-top: 8px;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .rs-storage-row {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .rs-browse-btn,
  .rs-auto-gen-btn {
    width: 100%;
    justify-content: center;
  }
}

/* --- Modern UI theme enhancements --- */
:root {
  --rs-bg: #ffffff;
  --rs-surface: rgba(255, 255, 255, 0.8);
  --rs-border: rgba(0,0,0,0.08);
  --rs-text: #1f2937;
  --rs-muted: #6b7280;
  --rs-primary: #3b82f6;
  --rs-primary-600: #2563eb;
  --rs-success: #16a34a;
  --rs-danger: #ef4444;
  --rs-warning: #f59e0b;
  --rs-shadow: 0 10px 30px rgba(2, 6, 23, 0.15);
}

@media (prefers-color-scheme: dark) {
  :root {
    --rs-bg: #0b1220;
    --rs-surface: rgba(17, 24, 39, 0.6);
    --rs-border: rgba(255,255,255,0.08);
    --rs-text: #e5e7eb;
    --rs-muted: #9ca3af;
    --rs-primary: #60a5fa;
    --rs-primary-600: #3b82f6;
    --rs-success: #22c55e;
    --rs-danger: #f87171;
    --rs-warning: #fbbf24;
    --rs-shadow: 0 12px 40px rgba(0,0,0,0.35);
    backdrop-filter: saturate(140%) contrast(110%);
  }
}

.rs-popup {
  background: var(--rs-surface);
  backdrop-filter: blur(14px) saturate(120%);
  -webkit-backdrop-filter: blur(14px) saturate(120%);
  border: 1px solid var(--rs-border);
  box-shadow: var(--rs-shadow);
}

.rs-popup-header {
  background: linear-gradient(135deg, var(--rs-primary), var(--rs-primary-600));
  border-bottom: 1px solid rgba(255,255,255,0.18);
}

.rs-popup-header h2 {
  letter-spacing: 0.2px;
}

.rs-popup-content {
  color: var(--rs-text);
}

.rs-popup-paper-list-container {
  border-color: var(--rs-border);
  background: rgba(255,255,255,0.55);
}

@media (prefers-color-scheme: dark) {
  .rs-popup-paper-list-container {
    background: rgba(17, 24, 39, 0.35);
  }
}

.rs-popup-paper-item {
  background: rgba(255,255,255,0.7);
  border: 1px solid var(--rs-border);
  transition: transform .15s ease, box-shadow .2s ease, border-color .2s ease;
}

.rs-popup-paper-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(2, 6, 23, 0.12);
  border-color: rgba(59,130,246,0.25);
}

@media (prefers-color-scheme: dark) {
  .rs-popup-paper-item { background: rgba(17,24,39,0.5); }
}

.rs-popup-paper-title { color: var(--rs-text); }
.rs-popup-paper-authors, .rs-popup-paper-year { color: var(--rs-muted); }

/* Buttons */
.rs-start-organize-btn,
.rs-browse-btn,
.rs-auto-gen-btn,
.rs-edit-prompt-btn {
  border-radius: 10px;
  box-shadow: 0 1px 2px rgba(2,6,23,0.08);
}

.rs-start-organize-btn {
  background: linear-gradient(135deg, var(--rs-success), #10b981);
}

.rs-start-organize-btn:hover {
  filter: brightness(1.03);
}

/* Toggle area cards */
.rs-config-section, .rs-storage-section {
  background: rgba(255,255,255,0.7);
  border: 1px solid var(--rs-border);
}

@media (prefers-color-scheme: dark) {
  .rs-config-section, .rs-storage-section { background: rgba(17,24,39,0.5); }
}

/* Inputs/selects */
.rs-language-select, .rs-standard-select, .rs-working-dir-input, .rs-task-dir-input {
  background: rgba(255,255,255,0.9);
  border: 1px solid var(--rs-border);
  color: var(--rs-text);
}

@media (prefers-color-scheme: dark) {
  .rs-language-select, .rs-standard-select, .rs-working-dir-input, .rs-task-dir-input {
    background: rgba(17,24,39,0.6);
  }
}

/* Close button refinement */
.rs-popup-close {
  color: #fff !important;
  backdrop-filter: blur(2px);
}

/* Action area (sticky) */
.rs-action-buttons-fixed {
  background: linear-gradient(180deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
}

@media (prefers-color-scheme: dark) {
  .rs-action-buttons-fixed {
    background: linear-gradient(180deg, rgba(17,24,39,0.85), rgba(17,24,39,0.6));
  }
}

/* Remove button visual without inline styles */
.rs-popup-paper-remove {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.08);
  transition: background-color .2s ease, transform .1s ease;
}

.rs-popup-paper-remove:hover { background: rgba(239, 68, 68, 0.18); }
.rs-popup-paper-remove:active { transform: scale(0.96); }

.rs-delete-icon { width: 18px; height: 18px; opacity: 0.75; }
.rs-popup-paper-remove:hover .rs-delete-icon { opacity: 1; }

/* Removing state */
.rs-popup-paper-remove.removing { background: rgba(239, 68, 68, 0.28); }


/* Subtle scrollbar */
.rs-popup-content, .rs-popup-paper-list-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(148,163,184,0.6) transparent;
}

.rs-popup-content::-webkit-scrollbar,
.rs-popup-paper-list-container::-webkit-scrollbar { height: 8px; width: 8px; }
.rs-popup-content::-webkit-scrollbar-thumb,
.rs-popup-paper-list-container::-webkit-scrollbar-thumb { background: rgba(148,163,184,0.6); border-radius: 8px; }
.rs-popup-content::-webkit-scrollbar-track,
.rs-popup-paper-list-container::-webkit-scrollbar-track { background: transparent; }

/* --- Compact action bar (one-line modules) --- */
.rs-action-bar {
  display: flex;
  align-items: stretch;
  gap: 6px;
  width: 100%;
  box-sizing: border-box;
}

/* symmetric side padding for the action bar line */
.rs-action-buttons .rs-action-bar {
  padding-left: 12px;
  padding-right: 12px;
}


.rs-action-module {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 6px;
  padding: 6px 8px;
  border: 1px solid var(--rs-border);
  border-radius: 8px;
  background: rgba(255,255,255,0.7);
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {
  .rs-action-module { background: rgba(17,24,39,0.5); }
}

.rs-dir-module { flex: 1; }

.rs-module-label {
  font-size: 12px;
  color: var(--rs-muted);
}

/* Compact input inside the action bar */
.rs-compact-input {
  flex: 1 1 auto;
  min-width: 0; /* allow shrinking inside flex to avoid overlap */
  padding: 6px 8px;
  height: 28px;
  font-size: 12px;
  border: 1px solid var(--rs-border);
  border-radius: 6px;
  background: rgba(255,255,255,0.9);
  color: var(--rs-text);
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {
  .rs-compact-input { background: rgba(17,24,39,0.6); }
}

/* Icon button for small actions */
.rs-icon-btn {
  width: 28px;
  height: 28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--rs-border);
  border-radius: 6px;
  background: rgba(255,255,255,0.7);
  cursor: pointer;
  transition: background-color .2s ease, transform .1s ease, box-shadow .2s ease;
}

.rs-icon-btn:hover { background: rgba(255,255,255,0.9); box-shadow: 0 1px 3px rgba(2,6,23,0.12); }
.rs-icon-btn:active { transform: scale(0.97); }

@media (prefers-color-scheme: dark) {
  .rs-icon-btn { background: rgba(17,24,39,0.5); }
  .rs-icon-btn:hover { background: rgba(17,24,39,0.65); }
}

/* Make toggles slightly smaller in the compact bar */
.rs-action-bar .rs-toggle-switch { width: 42px; height: 22px; }
.rs-action-bar .rs-toggle-slider:before { width: 16px; height: 16px; left: 3px; bottom: 3px; }
.rs-action-bar input:checked + .rs-toggle-slider:before { transform: translateX(20px); }


/* Module header/body for vertical arrangement within each compact module */
.rs-module-header {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* keep title and toggle close */
  gap: 8px;
}

/* prevent header from stretching children apart */
.rs-module-header > * { flex: 0 0 auto; }


.rs-module-title {
  font-size: 12px;
  color: var(--rs-muted);
  font-weight: 600;
}

.rs-module-body {
  margin-top: 6px;
}

/* Path row inside storage module */
.rs-path-row {
  display: flex;
  align-items: center;
  gap: 6px;
}

.rs-path-prefix {
  font-size: 12px;
  color: var(--rs-muted);
  white-space: nowrap;
  user-select: text;
}

/* Make translation/classification modules equal width, dir module grows */
.rs-module-translation, .rs-module-classification { width: 22%; min-width: 140px; }
.rs-dir-module { flex: 1 1 0; min-width: 180px; }

@media (max-width: 600px) {
  .rs-action-bar { flex-direction: column; align-items: stretch; }
  .rs-module-translation, .rs-module-classification, .rs-dir-module { width: 100%; min-width: unset; }
}

/* tighter internal paddings in header/body to save width */
.rs-module-header { gap: 6px; }

/* Force translation/classification selects to uniform width and smaller font */
.rs-module-body .rs-language-select,
.rs-module-body .rs-standard-select {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  font-size: 12px; /* 两号更小：原14 -> 12 */
  line-height: 1.2;
  padding: 4px 6px;
}

/* 显示4个字+省略号（收起状态） */
.rs-module-body .rs-language-select,
.rs-module-body .rs-standard-select {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 限制收起状态下可见字符宽度，约等于4个汉字宽度 */
.rs-module-translation .rs-language-select,
.rs-module-classification .rs-standard-select {
  inline-size: 4em; /* 近似4个汉字宽度 */
  max-inline-size: 100%;
}

/* 在窄布局或容器不足时，允许按100%撑满模块以避免挤压错位 */
@media (max-width: 600px) {
  .rs-module-translation .rs-language-select,
  .rs-module-classification .rs-standard-select {
    inline-size: 100%;
    max-inline-size: 100%;
  }
}

/* 页面通知组件样式 */
.rs-page-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10000;
  max-width: 400px;
  min-width: 300px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  animation: rs-notification-slide-in 0.5s ease-out;
}

.rs-page-notification.rs-hiding {
  animation: rs-notification-slide-out 0.5s ease-out forwards;
}

.rs-page-notification-title {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 15px;
}

.rs-page-notification-message {
  opacity: 0.9;
}

.rs-page-notification-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.rs-page-notification-close:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 任务完成通知的按钮区域 */
.rs-notification-buttons {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.rs-notification-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.rs-notification-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.rs-notification-button:active {
  transform: translateY(1px);
}

.rs-module-title { font-size: 12px; }
.rs-toggle-switch { width: 42px; height: 22px; }
.rs-toggle-slider:before { width: 16px; height: 16px; }
