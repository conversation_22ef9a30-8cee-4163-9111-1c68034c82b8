/**
 * PaperControls.css
 * 
 * Styles for the paper controls component
 */

.rs-controls {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  z-index: 10;
}

.rs-add-btn {
  width: 48px;
  height: 48px;
  border: none;
  background-color: transparent;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
  opacity: 0.8;
  transition: all 0.4s ease;
}

.rs-add-btn:hover {
  opacity: 1;
}

.rs-add-btn.rs-transitioning {
  opacity: 0;
}

