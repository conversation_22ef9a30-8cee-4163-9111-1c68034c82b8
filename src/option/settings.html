<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LitHelper 设置</title>
  <!-- 引入外部CSS文件 -->
  <link rel="stylesheet" href="./settings.css">
</head>
<body>
  <!-- 左侧导航栏 -->
  <div class="sidebar">
    <div class="nav-item active" data-section="ai-models">AI 模型</div>
    <div class="nav-item" data-section="summarization">摘要设置</div>
    <div class="nav-item" data-section="classification">分类管理</div>
    <div class="nav-item" data-section="tab-settings">标签页设置</div>
  </div>
  
  <!-- 右侧内容区 -->
  <div class="content">
    <!-- AI 模型设置部分 -->
    <div class="section active" id="ai-models">
      <header>
        <h1>AI 模型配置</h1>
        <div class="description">配置用于生成摘要的 AI 模型的 API 密钥和参数</div>
      </header>
      
      <div id="status-message" class="status"></div>
      
      <!-- 全局默认 AI 模型选择 -->
      <div class="form-group">
        <label for="default-ai-model">当前默认 AI 服务商</label>
        <select id="default-ai-model">
          <option value="">-- 请选择默认 AI 模型 --</option>
        </select>
        <div class="description">选择一个已启用并配置好 API Key 的模型作为全局默认使用的模型。</div>
      </div>
      
      <!-- 添加自定义模型按钮 -->
      <button id="add-custom-model" class="btn">
        <span>+ 添加自定义 AI 模型</span>
      </button>
      
      <!-- AI 模型卡片列表 -->
      <div class="model-cards" id="model-cards-container">
        <!-- 模型卡片将通过JS动态生成 -->
      </div>
    </div>
    
    <!-- 摘要设置部分 -->
    <div class="section" id="summarization">
      <header>
        <h1>摘要设置</h1>
        <div class="description">配置文献摘要的内容和格式</div>
      </header>
      
      <!-- 摘要类别选择 -->
      <div class="form-group">
        <label>选择摘要中包含的模块:</label>
        <div class="checkbox-container" id="summary-categories">
          <!-- 摘要类别将通过JS动态生成 -->
        </div>
      </div>
      
      <!-- 批处理设置 -->
      <div class="form-group">
        <label for="max-papers">每批次最大处理文献数:</label>
        <input type="number" id="max-papers" min="1" max="20">
        <div class="description">一次处理的最大文献数量，较大的数值可能会导致处理时间延长。</div>
      </div>
      
      <!-- 内容包含选项 -->
      <div class="form-group">
        <label>内容包含选项:</label>
        <div class="checkbox-item">
          <input type="checkbox" id="include-abstract">
          <label for="include-abstract">包含原文摘要</label>
        </div>
        <div class="checkbox-item">
          <input type="checkbox" id="include-citations">
          <label for="include-citations">包含参考文献</label>
        </div>
      </div>
    </div>

    <!-- 分类管理部分 -->
    <div class="section" id="classification">
      <header>
        <h1>分类管理</h1>
        <div class="description">管理论文分类标准和编辑分类提示词</div>
      </header>

      <!-- 添加新分类标准按钮 -->
      <button id="add-classification-standard" class="btn">
        <span>+ 添加分类标准</span>
      </button>

      <!-- 分类标准列表 -->
      <div class="classification-standards" id="classification-standards-container">
        <!-- 分类标准卡片将通过JS动态生成 -->
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <button id="reset-defaults" class="btn btn-secondary">恢复默认设置</button>
      <button id="save-settings" class="btn">保存更改</button>
    </div>
  </div>
  
  <!-- 添加自定义模型的模态框 -->
  <div class="modal" id="custom-model-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">添加自定义 AI 模型</h2>
        <button class="modal-close" id="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="model-name" class="required">自定义模型名称</label>
          <input type="text" id="model-name" placeholder="例如: Azure OpenAI, Baidu">
          <div class="description">为这个 AI 服务商起一个易识别的名称</div>
        </div>
        
        <div class="form-group">
          <label for="model-api-key" class="required">API 密钥</label>
          <input type="password" id="model-api-key" placeholder="输入API密钥">
          <div class="description">从服务商获取的 API 密钥，用于授权访问</div>
        </div>
        
        <div class="form-group">
          <label for="model-api-url" class="required">API 地址</label>
          <input type="text" id="model-api-url" placeholder="https://api.example.com">
          <div class="description">模型 API 的完整 URL 地址</div>
        </div>
        
        <div class="form-group">
          <label for="model-available-models" class="required">可用模型</label>
          <textarea id="model-available-models" placeholder="输入模型名称，多个模型用逗号或换行分隔" rows="3"></textarea>
          <div class="description">输入该服务商提供的模型名称，例如: model-name-1, model-name-2</div>
        </div>
        
        <div class="form-group">
          <label for="model-default-model" class="required">默认选用模型</label>
          <select id="model-default-model">
            <option value="">-- 请先输入可用模型 --</option>
          </select>
          <div class="description">设置该服务商默认使用的模型</div>
        </div>
        
        <div class="form-group">
          <label for="model-max-tokens" class="required">最大生成 Token 数</label>
          <input type="number" id="model-max-tokens" value="2000" min="100" max="10000">
          <div class="description">单次请求最大生成的 token 数量</div>
        </div>
        
        <div class="form-group">
          <label for="model-temperature" class="required">温度</label>
          <div class="slider-container">
            <input type="range" id="model-temperature" min="0" max="1" step="0.1" value="0.7">
            <span class="slider-value" id="temperature-value">0.7</span>
          </div>
          <div class="description">控制生成文本的随机性，值越高结果越多样，值越低结果越确定</div>
        </div>
        
        <div class="form-group">
          <div class="checkbox-item">
            <input type="checkbox" id="model-active" checked>
            <label for="model-active">立即启用此模型</label>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="modal-cancel">取消</button>
        <button class="btn" id="modal-add">添加</button>
      </div>
    </div>
  </div>
  
  <!-- 确认删除模态框 -->
  <div class="modal" id="confirm-delete-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">确认删除</h2>
        <button class="modal-close" id="delete-modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <p>确定要删除 <span id="delete-model-name"></span> 吗？此操作不可撤销。</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" id="delete-modal-cancel">取消</button>
        <button class="btn" id="delete-modal-confirm">删除</button>
      </div>
    </div>
  </div>
  
</body>
</html> 