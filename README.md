![Demo](static/demo.gif)

# LitHelper

这是一个浏览器扩展，用于提取和总结学术论文。


## 项目结构

```
src/
  background/               # 扩展后台服务
    background.js           # 后台主入口
    feature/                # 论文相关功能
      paperBoxManager.js    # 论文盒管理
      paperMetadataService.js   # 论文元数据服务
      paperOrganizationService.js # 论文组织服务
    service/                # 核心服务
      baseHandler.js        # 基础处理器
      htmlParserService.js  # HTML解析服务
      httpService.js        # HTTP服务
      messageService.js     # 消息服务
      offscreenManager.js   # 离屏管理
      taskService.js        # 任务服务
      taskHandler/          # 任务处理器
        aiCrawlerTaskHandler.js    # AI爬虫任务处理器
        aiExtractorTaskHandler.js  # AI提取任务处理器
        organizeTaskHandler.js     # 组织任务处理器
    util/                   # 工具类
      downloadService.js    # 下载工具
      httpService.js        # HTTP工具
    view/                   # 视图相关
      contextMenuService.js # 右键菜单服务
  content/                  # 内容脚本
    content.js              # 内容主入口
    extractors/             # 元素/信息提取器
      elementExtractors/
        googleScholarElementExactor.js
      infoExtractors/
        googleScholarInfoExactor.js
    platforms/              # 平台适配
      base/
        PlatformAdapter.js
        SearchPlatformAdapter.js
      search/
        GoogleScholarAdapter.js
    ui/                     # UI组件
      UIManager.js
      components/
        FloatingButton.js
        PaperControls.js
        PopupWindow.js
        SummaryContainer.js
  model/                    # 数据模型
    CssSelector.js
    Paper.js
    PlatformSelector.js
    RegexSelector.js
    Result.js
    Selector.js
    summary.js
    task.js
    config/
      website/
  offscreen/                # 离屏相关
    offscreen.html
    offscreen.js
  option/                   # 选项页
    controller.js
    index.js
    settings.css
    settings.html
    view.js
  popup/                    # 弹窗
    popup.html
    popup.js
  service/                  # 服务
    aiService.js
    configService.js
    fileManagementService.js
    runTimeDataService.js
  util/                     # 通用工具
    htmlParser.js
    logger.js
    message.js
    storage.js
assets/
  icons/                    # 图标资源
    icon16.png
    icon48.png
    icon128.png
    openai-logo.png
    google-logo.png
    anthropic-logo.png
    deepseek-logo.png
    delete-icon.svg
manifest.json               # 扩展清单
webpack.config.js           # 构建配置
package.json                # 项目依赖
README.md                   # 项目说明
```




## 开发指南

```bash
# 安装依赖
npm install

# 开发模式构建并监视更改
npm run dev

# 生产环境构建
npm run build

# 运行代码检查
npm run lint

# 运行测试
npm test
```